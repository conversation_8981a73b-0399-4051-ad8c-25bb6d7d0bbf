// 助记词列表 - 请在此处添加您的助记词
const mnemonics = [
    "cause assist obvious place include boring wife park glory replace wise raccoon",
];

// 配置
const config = {
    delay: 1500, // 每次导入间隔（毫秒）
    extensionId: 'mcohilncbfahbmgdjkbpemcciiolgcge'
};

// 工具函数
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const log = (msg, type = 'info') => {
    const time = new Date().toLocaleTimeString();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50', 
        error: '#F44336',
        warning: '#FF9800'
    };
    console.log(`%c[${time}] ${msg}`, `color: ${colors[type]}; font-weight: bold;`);
};

// 获取iframe文档对象
const getIframeDocument = () => {
    try {
        // 获取外层iframe
        const outerIframe = document.querySelector('#ui-ses-iframe');
        if (!outerIframe) {
            throw new Error('未找到外层iframe');
        }
        
        const iframeDoc = outerIframe.contentDocument || outerIframe.contentWindow.document;
        if (!iframeDoc) {
            throw new Error('无法访问iframe文档');
        }
        
        return iframeDoc;
    } catch (error) {
        log(`获取iframe文档失败: ${error.message}`, 'error');
        return null;
    }
};

// 等待元素出现（支持iframe）
const waitForElement = (selector, timeout = 5000, useIframe = true) => {
    return new Promise((resolve, reject) => {
        const start = Date.now();
        const check = () => {
            let el;
            if (useIframe) {
                const iframeDoc = getIframeDocument();
                if (iframeDoc) {
                    el = iframeDoc.querySelector(selector);
                }
            } else {
                el = document.querySelector(selector);
            }
            
            if (el) return resolve(el);
            if (Date.now() - start > timeout) return reject(new Error(`元素未找到: ${selector}`));
            setTimeout(check, 100);
        };
        check();
    });
};

// 有效的粘贴模拟 - 使用ClipboardEvent（支持iframe）
const effectivePasteSimulation = async (targetElement, text, iframeDoc) => {
    log(`导入助记词: [${text}]`, 'info');

    try {
        targetElement.focus();
        targetElement.click();
        await wait(100);

        // 在iframe文档中创建事件
        const pasteEvent = new (iframeDoc.defaultView.ClipboardEvent)('paste', {
            bubbles: true,
            cancelable: true,
            composed: true
        });

        // 模拟clipboardData
        Object.defineProperty(pasteEvent, 'clipboardData', {
            value: {
                getData: (format) => {
                    if (format === 'text/plain' || format === 'text') {
                        return text;
                    }
                    return '';
                },
                types: ['text/plain'],
                items: [{
                    kind: 'string',
                    type: 'text/plain',
                    getAsString: (callback) => callback(text)
                }]
            },
            writable: false,
            configurable: false
        });

        const result = targetElement.dispatchEvent(pasteEvent);

        // 等待OKX处理
        await wait(500);
        
        return true;

    } catch (error) {
        log(`导入助记词失败: ${error.message}`, 'error');
        return false;
    }
};

// 导入单个助记词
const importMnemonic = async (mnemonic, index) => {
    try {
        log(`开始导入第 ${index + 1}/${mnemonics.length} 个助记词`, 'info');
        
        // 跳转到导入页面
        const importUrl = `chrome-extension://${config.extensionId}/fullscreen.html#/wallet-add/import-with-seed-phrase-and-private-key`;
        window.location.href = importUrl;
        await wait(1000); // 增加等待时间确保iframe加载
        
        // 等待iframe加载完成
        await waitForElement('#ui-ses-iframe', 1000, false);
        await wait(500);
        
        // 获取iframe文档
        const iframeDoc = getIframeDocument();
        if (!iframeDoc) {
            throw new Error('无法访问iframe文档');
        }
        
        // 等待助记词输入框出现（新版选择器）
        await waitForElement('input[data-testid="import-seed-phrase-or-private-key-page-seed-phrase-input"]', 5000, true);
        
        // 获取第一个输入框
        const firstInput = iframeDoc.querySelector('input[data-testid="import-seed-phrase-or-private-key-page-seed-phrase-input"]');
        if (!firstInput) {
            throw new Error('未找到助记词输入框');
        }
        
        // 使用有效的粘贴模拟
        const pasteSuccess = await effectivePasteSimulation(firstInput, mnemonic, iframeDoc);

        await wait(500);
        
        // 等待确认按钮（新版选择器）
        const confirmBtn = await waitForElement('button[data-testid="import-seed-phrase-or-private-key-page-confirm-button"]', 5000, true);

        if (confirmBtn.disabled) {
            // 检查错误消息（如果有的话）
            const errorMsg = iframeDoc.querySelector('.error-message, .warning-message');
            if (errorMsg) {
                throw new Error(`导入失败: ${errorMsg.textContent}`);
            }
            throw new Error('确认按钮被禁用，可能助记词格式不正确');
        }

        confirmBtn.click();
        
        // 等待导入完成（检查URL变化）
        const startTime = Date.now();
        while (Date.now() - startTime < 3000) {
            if (!window.location.href.includes('import-with-seed-phrase-and-private-key')) {
                log(`第 ${index + 1} 个助记词导入成功`, 'success');
                return true;
            }
            await wait(500);
        }

        throw new Error('导入超时');
        
    } catch (error) {
        log(`第 ${index + 1} 个助记词导入失败: ${error.message}`, 'error');
        return false;
    }
};

// 批量导入主函数
const batchImport = async () => {
    if (mnemonics.length === 0) {
        log('助记词列表为空，请先添加助记词', 'error');
        return;
    }
    
    log('='.repeat(50), 'info');
    log(`开始批量导入 ${mnemonics.length} 个助记词`, 'info');
    log('='.repeat(50), 'info');
    
    let successCount = 0;
    let failureCount = 0;
    const startTime = Date.now();
    
    for (let i = 0; i < mnemonics.length; i++) {
        const mnemonic = mnemonics[i].trim();
        if (!mnemonic) continue;
        
        const result = await importMnemonic(mnemonic, i);
        if (result) {
            successCount++;
        } else {
            failureCount++;
        }
        
        // 等待间隔
        if (i < mnemonics.length - 1) {
            await wait(config.delay);
        }
    }
    
    // 统计结果
    const totalTime = Math.round((Date.now() - startTime) / 1000);
    log('='.repeat(50), 'info');
    log('批量导入完成！', 'success');
    log(`总耗时: ${totalTime} 秒`, 'info');
    log(`成功: ${successCount} 个`, 'success');
    log(`失败: ${failureCount} 个`, 'error');
    log('='.repeat(50), 'info');
};

log('='.repeat(50), 'info');
log('OKX钱包批量导入脚本已启动', 'success');

batchImport();
